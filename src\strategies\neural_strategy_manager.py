"""
Neural Strategy Manager
Dynamically manages and switches between trading strategies based on neural learning
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
from enum import Enum

from .base import BaseStrategy, SignalData, MarketContext
from .momentum import MomentumStrategy
from .mean_reversion import MeanReversionStrategy
from .adaptive_neural_strategy import AdaptiveNeuralStrategy

logger = logging.getLogger(__name__)

class StrategyPerformanceMetric(Enum):
    """Strategy performance metrics for evaluation"""
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"
    SHARPE_RATIO = "sharpe_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    TOTAL_RETURN = "total_return"
    VOLATILITY = "volatility"

@dataclass
class StrategyPerformance:
    """Strategy performance tracking"""
    strategy_name: str
    total_trades: int = 0
    winning_trades: int = 0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    last_updated: datetime = None
    recent_performance: List[float] = None
    
    def __post_init__(self):
        if self.recent_performance is None:
            self.recent_performance = []
        if self.last_updated is None:
            self.last_updated = datetime.now()
    
    @property
    def win_rate(self) -> float:
        return self.winning_trades / max(self.total_trades, 1)
    
    @property
    def profit_factor(self) -> float:
        wins = [p for p in self.recent_performance if p > 0]
        losses = [abs(p) for p in self.recent_performance if p < 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 1
        
        return avg_win / avg_loss if avg_loss > 0 else 0

class NeuralStrategyManager:
    """
    Neural-powered strategy manager that dynamically selects and adapts strategies
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize available strategies
        self.strategies = {
            'momentum': MomentumStrategy(self.config.get('momentum', {})),
            'mean_reversion': MeanReversionStrategy(self.config.get('mean_reversion', {})),
            'adaptive_neural': AdaptiveNeuralStrategy(self.config.get('adaptive_neural', {}))
        }
        
        # Strategy performance tracking
        self.strategy_performance = {}
        self.performance_history = {}
        for strategy_name in self.strategies.keys():
            self.strategy_performance[strategy_name] = StrategyPerformance(strategy_name)
            self.performance_history[strategy_name] = deque(maxlen=1000)
        
        # Current active strategy
        self.active_strategy = 'adaptive_neural'  # Start with most advanced
        self.strategy_switch_threshold = 0.1  # 10% performance difference
        
        # Neural strategy selection
        self.neural_selector = None
        self.neural_enabled = False
        
        # Performance evaluation
        self.evaluation_window = 50  # Number of trades to evaluate
        self.min_trades_for_evaluation = 20
        self.strategy_evaluation_interval = timedelta(hours=6)
        self.last_evaluation = datetime.now()
        
        # Strategy switching history
        self.strategy_switches = deque(maxlen=100)
        
        # Market regime awareness
        self.market_regime_weights = {
            'trending_up': {'momentum': 0.6, 'adaptive_neural': 0.3, 'mean_reversion': 0.1},
            'trending_down': {'momentum': 0.6, 'adaptive_neural': 0.3, 'mean_reversion': 0.1},
            'sideways': {'mean_reversion': 0.5, 'adaptive_neural': 0.4, 'momentum': 0.1},
            'high_volatility': {'adaptive_neural': 0.7, 'momentum': 0.2, 'mean_reversion': 0.1},
            'breakout': {'momentum': 0.5, 'adaptive_neural': 0.4, 'mean_reversion': 0.1}
        }
        
        self._init_neural_selector()
        
        logger.info("NeuralStrategyManager initialized with dynamic strategy selection")
    
    def _init_neural_selector(self):
        """Initialize neural network for strategy selection"""
        try:
            # Temporarily disable neural selector to avoid hanging
            # TODO: Fix neural component initialization
            logger.info("Neural strategy selector temporarily disabled for stability")
            self.neural_enabled = False
            self.neural_selector = None

            # # Try to import neural components
            # from ..neural.reinforcement_learning import ReinforcementLearningAgent
            #
            # # Initialize RL agent for strategy selection
            # self.neural_selector = ReinforcementLearningAgent(
            #     state_size=15,  # Market features for strategy selection
            #     action_size=len(self.strategies),  # Number of strategies
            #     learning_rate=0.001
            # )
            #
            # self.neural_enabled = True
            # logger.info("Neural strategy selector initialized")

        except Exception as e:
            logger.warning(f"Neural components not available - using rule-based strategy selection: {e}")
            self.neural_enabled = False
    
    async def get_trading_signal(self, market_context: MarketContext) -> Optional[SignalData]:
        """
        Get TIME-AWARE PROFITABLE trading signal with optimal timing
        """
        try:
            # TIME FACTOR 1: Check if we should hold current positions longer
            if await self._should_hold_current_positions():
                logger.info("[TIME] Holding current positions for optimal profit timing")
                return None

            # TIME FACTOR 2: Check market timing conditions
            if not await self._is_optimal_trading_time(market_context):
                logger.debug("[TIME] Market timing not optimal - waiting for better entry")
                return None

            # Evaluate and potentially switch strategies
            if self._should_evaluate_strategies():
                await self._evaluate_and_switch_strategies(market_context)

            # Get signal from active strategy with TIME ENHANCEMENT
            active_strategy = self.strategies[self.active_strategy]
            signal = await active_strategy.analyze_market(market_context)

            # TIME FACTOR 3: Apply time-based signal filtering for profitability
            if signal:
                signal = await self._apply_time_based_profit_optimization(signal, market_context)

                if signal:  # Signal survived time-based filtering
                    # Store time-based optimization info in reasoning field
                    time_horizon = self._calculate_optimal_time_horizon(signal, market_context)
                    profit_target_time = self._calculate_profit_target_time(signal)

                    signal.reasoning = (f"TIME-OPTIMIZED {self.active_strategy} strategy | "
                                      f"Time horizon: {time_horizon} | "
                                      f"Profit target: {profit_target_time} | "
                                      f"Strategy confidence: {self._get_strategy_confidence(self.active_strategy):.2f} | "
                                      f"Manager: time_aware_v2")

                    # Track last trade time for position holding logic
                    self.last_trade_time = datetime.now()

                    logger.info(f"[PROFITABLE] Generated time-optimized signal: {signal.action} {signal.symbol} "
                              f"(confidence: {signal.confidence:.2f}, time_horizon: {time_horizon})")

            return signal

        except Exception as e:
            logger.error(f"Error getting trading signal from strategy manager: {e}")
            return None
    
    async def _evaluate_and_switch_strategies(self, market_context: MarketContext):
        """Evaluate strategy performance and switch if needed"""
        try:
            # Calculate performance scores for all strategies
            performance_scores = {}
            
            for strategy_name, performance in self.strategy_performance.items():
                if performance.total_trades >= self.min_trades_for_evaluation:
                    score = self._calculate_strategy_score(performance, market_context)
                    performance_scores[strategy_name] = score
                else:
                    # Give new strategies a neutral score
                    performance_scores[strategy_name] = 0.5
            
            # Neural strategy selection if available
            if self.neural_enabled and self.neural_selector:
                best_strategy = await self._neural_strategy_selection(market_context, performance_scores)
            else:
                # Rule-based strategy selection
            # Switch strategy if needed
            if best_strategy != self.active_strategy:
                await self._switch_strategy(best_strategy, performance_scores)
            
            self.last_evaluation = datetime.now()
            
        except Exception as e:
            logger.error(f"Error evaluating strategies: {e}")
    
    def _calculate_strategy_score(self, performance: StrategyPerformance, market_context: MarketContext) -> float:
        """Calculate comprehensive strategy performance score"""
        try:
            # Base performance metrics
            win_rate_score = performance.win_rate
            profit_factor_score = min(performance.profit_factor / 2.0, 1.0)  # Normalize
            sharpe_score = min((performance.sharpe_ratio + 1) / 3.0, 1.0)  # Normalize
            drawdown_score = max(0, 1.0 - performance.max_drawdown / 0.2)  # Penalize high drawdown
            
            # Recent performance weight
            recent_weight = 0.6
            historical_weight = 0.4
            
            # Recent performance (last 10 trades)
            recent_pnl = performance.recent_performance[-10:] if len(performance.recent_performance) >= 10 else performance.recent_performance
            recent_score = 0.5
            if recent_pnl:
                recent_wins = sum(1 for pnl in recent_pnl if pnl > 0)
                recent_score = recent_wins / len(recent_pnl)
            
            # Historical performance
            historical_score = (win_rate_score + profit_factor_score + sharpe_score + drawdown_score) / 4
            
            # Combined score
            total_score = (recent_score * recent_weight) + (historical_score * historical_weight)
            
            # Market regime adjustment
            current_regime = market_context.sentiment_data.get('market_regime', 'sideways')
            regime_weights = self.market_regime_weights.get(current_regime, {})
            regime_weight = regime_weights.get(performance.strategy_name, 0.33)
            
            # Apply regime weight
            final_score = total_score * (0.7 + regime_weight * 0.3)
            
            return min(max(final_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating strategy score: {e}")
            return 0.5
    
    async def _neural_strategy_selection(self, market_context: MarketContext, 
                                       performance_scores: Dict[str, float]) -> str:
        """Use neural network to select best strategy"""
        try:
            # Prepare state vector for neural network
            state = self._prepare_strategy_selection_state(market_context, performance_scores)
            
            # Get neural network prediction
            if hasattr(self.neural_selector, 'predict'):
                action_probs = self.neural_selector.predict(state)
                
                # Select strategy based on probabilities
                strategy_names = list(self.strategies.keys())
                best_strategy_idx = np.argmax(action_probs)
                best_strategy = strategy_names[best_strategy_idx]
                
                logger.debug(f"Neural strategy selection: {best_strategy} "
                           f"(confidence: {action_probs[best_strategy_idx]:.3f})")
                
                return best_strategy
            else:
                # Fallback to rule-based
                return await self._rule_based_strategy_selection(market_context, performance_scores)
                
        except Exception as e:
            logger.error(f"Error in neural strategy selection: {e}")
            return await self._rule_based_strategy_selection(market_context, performance_scores)
    
    async def _rule_based_strategy_selection(self, market_context: MarketContext, 
                                           performance_scores: Dict[str, float]) -> str:
        """Rule-based strategy selection fallback"""
        try:
            # Get current market regime
            current_regime = market_context.sentiment_data.get('market_regime', 'sideways')
            
            # Calculate weighted scores based on regime and performance
            weighted_scores = {}
            regime_weights = self.market_regime_weights.get(current_regime, {})
            
            for strategy_name, performance_score in performance_scores.items():
                regime_weight = regime_weights.get(strategy_name, 0.33)
                weighted_score = (performance_score * 0.7) + (regime_weight * 0.3)
                weighted_scores[strategy_name] = weighted_score
            
            # Select best strategy
            best_strategy = max(weighted_scores.items(), key=lambda x: x[1])[0]
            
            logger.debug(f"Rule-based strategy selection: {best_strategy} "
                        f"(score: {weighted_scores[best_strategy]:.3f})")
            
            return best_strategy
            
        except Exception as e:
            logger.error(f"Error in rule-based strategy selection: {e}")
            return 'adaptive_neural'  # Safe fallback

    async def update_performance(self, strategy_name: str, trade_result: Dict[str, Any]):
        """Update strategy performance with trade result - ACTIVE LEARNING"""
        try:
            if strategy_name not in self.performance_history:
                self.performance_history[strategy_name] = []

            # Record detailed trade result for learning
            trade_record = {
                'timestamp': datetime.now(),
                'result': trade_result,
                'profit_loss': trade_result.get('profit_loss', 0),
                'success': trade_result.get('success', False),
                'confidence': trade_result.get('confidence', 0.5),
                'market_conditions': trade_result.get('market_conditions', {}),
                'execution_time': trade_result.get('execution_time', 0),
                'slippage': trade_result.get('slippage', 0)
            }

            self.performance_history[strategy_name].append(trade_record)

            # Calculate real-time performance metrics
            await self._calculate_real_time_metrics(strategy_name)

            # Keep only recent history
            max_history = 1000
            if len(self.performance_history[strategy_name]) > max_history:
                self.performance_history[strategy_name] = self.performance_history[strategy_name][-max_history:]

            # ACTIVE LEARNING: Update neural network with trade outcome
            if self.neural_enabled and hasattr(self, 'neural_selector'):
                await self._update_neural_network(strategy_name, trade_result)

            # Log learning progress
            logger.info(f"🧠 [ACTIVE-LEARNING] Updated {strategy_name} performance: "
                       f"Success: {trade_record['success']}, P&L: {trade_record['profit_loss']:.4f}")

        except Exception as e:
            logger.error(f"Error updating performance: {e}")

    async def _calculate_real_time_metrics(self, strategy_name: str):
        """Calculate real-time performance metrics for active learning"""
        try:
            if strategy_name not in self.performance_history:
                return

            history = self.performance_history[strategy_name]
            if len(history) < 2:
                return

            # Calculate win rate
            recent_trades = history[-50:]  # Last 50 trades
            wins = sum(1 for trade in recent_trades if trade['success'])
            win_rate = wins / len(recent_trades) if recent_trades else 0

            # Calculate Sharpe ratio
            returns = [trade['profit_loss'] for trade in recent_trades]
            if len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = mean_return / std_return if std_return > 0 else 0
            else:
                sharpe_ratio = 0

            # Calculate drawdown
            cumulative_pnl = np.cumsum([trade['profit_loss'] for trade in history])
            peak = np.maximum.accumulate(cumulative_pnl)
            drawdown = (peak - cumulative_pnl) / peak
            max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0

            # Update performance tracking
            if strategy_name not in self.strategy_performance:
                self.strategy_performance[strategy_name] = StrategyPerformance(strategy_name)

            perf = self.strategy_performance[strategy_name]
            perf.total_trades = len(history)
            perf.winning_trades = wins
            perf.total_pnl = sum(trade['profit_loss'] for trade in history)
            perf.max_drawdown = max_drawdown
            perf.sharpe_ratio = sharpe_ratio
            perf.last_updated = datetime.now()

            # Log metrics for verification
            logger.info(f"📊 [PERFORMANCE-METRICS] {strategy_name}: "
                       f"Win Rate: {win_rate:.3f}, Sharpe: {sharpe_ratio:.3f}, "
                       f"Max DD: {max_drawdown:.3f}, Total P&L: {perf.total_pnl:.4f}")

        except Exception as e:
            logger.error(f"Error calculating real-time metrics: {e}")

    async def _update_neural_network(self, strategy_name: str, trade_result: Dict[str, Any]):
        """Update neural network with trade outcome for active learning"""
        try:
            # This would update the neural network with the trade result
            # For now, we'll log the learning activity
            logger.info(f"🧠 [NEURAL-LEARNING] Processing trade outcome for {strategy_name}")
            logger.info(f"🧠 [NEURAL-LEARNING] Result: {trade_result.get('success', False)}, "
                       f"Confidence: {trade_result.get('confidence', 0.5):.3f}")

            # In a full implementation, this would:
            # 1. Extract features from market conditions
            # 2. Create training sample from trade outcome
            # 3. Update neural network weights
            # 4. Adjust strategy selection probabilities

        except Exception as e:
            logger.error(f"Error updating neural network: {e}")
    
    def _prepare_strategy_selection_state(self, market_context: MarketContext, 
                                        performance_scores: Dict[str, float]) -> np.ndarray:
        """Prepare state vector for neural strategy selection"""
        try:
            # Market features from technical indicators
            technical_data = market_context.technical_indicators.get('BTC-USD', {})
            price = float(technical_data.get('price', 45000))
            volume = float(technical_data.get('volume', 1000000))
            volatility = float(technical_data.get('volatility', 0.02))
            
            # Performance scores
            momentum_score = performance_scores.get('momentum', 0.5)
            mean_reversion_score = performance_scores.get('mean_reversion', 0.5)
            adaptive_score = performance_scores.get('adaptive_neural', 0.5)
            
            # Market sentiment
            sentiment = market_context.sentiment_data.get('overall', 0.0)
            news_impact = market_context.sentiment_data.get('high_impact_count', 0) / 10.0
            
            # Recent strategy performance
            recent_switches = len([s for s in self.strategy_switches 
                                 if s['timestamp'] > datetime.now() - timedelta(hours=24)])
            
            # Time features
            hour = datetime.now().hour / 24.0
            day_of_week = datetime.now().weekday() / 7.0
            
            state = np.array([
                price / 100000.0,  # Normalize price
                volume / 1000000.0,  # Normalize volume
                volatility,
                momentum_score,
                mean_reversion_score,
                adaptive_score,
                sentiment,
                news_impact,
                recent_switches / 10.0,
                hour,
                day_of_week,
                # Add current active strategy as one-hot
                1.0 if self.active_strategy == 'momentum' else 0.0,
                1.0 if self.active_strategy == 'mean_reversion' else 0.0,
                1.0 if self.active_strategy == 'adaptive_neural' else 0.0,
                len(self.strategy_switches) / 100.0  # Strategy switch frequency
            ], dtype=np.float32)
            
            return state
            
        except Exception as e:
            logger.error(f"Error preparing strategy selection state: {e}")
            return np.zeros(15, dtype=np.float32)

    # ==================== TIME-BASED PROFITABLE TRADING METHODS ====================

    async def _should_hold_current_positions(self) -> bool:
        """
        TIME FACTOR 1: Determine if we should hold current positions for optimal profit timing
        """
        try:
            # Check if we have recent positions that should be held longer
            current_time = datetime.now()

            # OPTIMIZED: Reduce minimum time between trades for higher frequency (was 2 minutes, now 30 seconds)
            # This allows for more frequent profitable opportunities while preventing over-trading
            if hasattr(self, 'last_trade_time'):
                time_since_last_trade = (current_time - self.last_trade_time).total_seconds()
                if time_since_last_trade < 30:  # 30 seconds minimum for high-frequency profitable trading
                    return True

            # Check if we have open positions that are approaching profit targets
            # This would integrate with position tracking system
            # For now, implement basic time-based holding logic

            return False

        except Exception as e:
            logger.error(f"Error checking position hold status: {e}")
            return False

    async def _is_optimal_trading_time(self, market_context: MarketContext) -> bool:
        """
        ENHANCED TIME FACTOR 2: Advanced optimal trading time analysis
        """
        try:
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute
            weekday = current_time.weekday()

            # OPTIMIZED: Market session awareness - reduced threshold for more opportunities
            market_session = self._get_current_market_session(current_time)
            session_score = self._calculate_session_trading_score(market_session, current_time)

            if session_score < 0.15:  # Reduced from 0.3 to 0.15 - allow more trading sessions
                logger.debug(f"[TIME] Poor trading session: {market_session} (score: {session_score:.2f})")
                return False

            # ENHANCED: Avoid low liquidity periods with more precision
            if self._is_low_liquidity_period(hour, minute, weekday):
                logger.debug(f"[TIME] Low liquidity period detected: {hour:02d}:{minute:02d}")
                return False

            # ENHANCED: Market microstructure timing
            if not await self._check_microstructure_timing(market_context):
                logger.debug("[TIME] Microstructure timing not optimal")
                return False

            # ENHANCED: Volatility and volume analysis with time decay
            if not self._check_volatility_volume_timing(market_context, current_time):
                logger.debug("[TIME] Volatility/volume timing not optimal")
                return False

            # ENHANCED: News and event timing
            if not await self._check_news_event_timing(market_context, current_time):
                logger.debug("[TIME] News/event timing not optimal")
                return False

            # ENHANCED: Momentum and trend timing
            if not self._check_momentum_timing(market_context):
                logger.debug("[TIME] Momentum timing not optimal")
                return False

            logger.debug(f"[TIME] Optimal trading time confirmed (session: {market_session}, score: {session_score:.2f})")
            return True

        except Exception as e:
            logger.error(f"Error checking optimal trading time: {e}")
            return True  # Default to allow trading

    def _get_current_market_session(self, current_time: datetime) -> str:
        """Determine current market session"""
        try:
            # Convert to UTC for consistent session detection
            utc_hour = current_time.hour  # Assuming input is already UTC

            if 0 <= utc_hour < 8:
                return "asian_session"
            elif 8 <= utc_hour < 16:
                return "european_session"
            elif 16 <= utc_hour < 24:
                return "american_session"
            else:
                return "overlap_session"

        except Exception as e:
            logger.debug(f"Error determining market session: {e}")
            return "unknown_session"

    def _calculate_session_trading_score(self, session: str, current_time: datetime) -> float:
        """Calculate trading score for current session"""
        try:
            hour = current_time.hour
            minute = current_time.minute
            weekday = current_time.weekday()

            # Base scores for different sessions
            session_scores = {
                "asian_session": 0.6,      # Lower volatility, good for range trading
                "european_session": 0.9,   # High activity, good for breakouts
                "american_session": 0.8,   # High volume, good for trend following
                "overlap_session": 0.95,   # Highest liquidity
                "unknown_session": 0.5
            }

            base_score = session_scores.get(session, 0.5)

            # Adjust for specific times within sessions
            if session == "european_session":
                if 8 <= hour <= 10:  # European open
                    base_score += 0.1
                elif 14 <= hour <= 16:  # Pre-US overlap
                    base_score += 0.05
            elif session == "american_session":
                if 16 <= hour <= 18:  # US open
                    base_score += 0.1
                elif 20 <= hour <= 22:  # Active trading
                    base_score += 0.05

            # Reduce score for weekends
            if weekday >= 5:  # Saturday/Sunday
                base_score *= 0.3

            # Reduce score for very early/late hours
            if hour < 6 or hour > 22:
                base_score *= 0.7

            return min(base_score, 1.0)

        except Exception as e:
            logger.debug(f"Error calculating session score: {e}")
            return 0.5

    def _is_low_liquidity_period(self, hour: int, minute: int, weekday: int) -> bool:
        """Enhanced low liquidity period detection"""
        try:
            # Weekend trading (reduced liquidity)
            if weekday >= 5:  # Saturday/Sunday
                return True

            # Very late night / early morning (22:00-06:00 UTC)
            if hour >= 22 or hour <= 6:
                return True

            # Lunch hours in major markets (12:00-13:00 UTC for Europe)
            if hour == 12:
                return True

            # Market close periods (specific minutes around major closes)
            if hour == 21 and 55 <= minute <= 59:  # US market close
                return True
            if hour == 16 and 25 <= minute <= 35:  # European market close
                return True

            return False

        except Exception as e:
            logger.debug(f"Error checking liquidity period: {e}")
            return False

    async def _check_microstructure_timing(self, market_context: MarketContext) -> bool:
        """Check market microstructure for optimal timing"""
        try:
            # Check order book imbalance
            order_books = market_context.order_books
            for exchange, books in order_books.items():
                for symbol, book in books.items():
                    if isinstance(book, dict):
                        bids = book.get('bids', [])
                        asks = book.get('asks', [])

                        if bids and asks:
                            # Calculate order book imbalance
                            bid_volume = sum(float(bid[1]) for bid in bids[:5])
                            ask_volume = sum(float(ask[1]) for ask in asks[:5])

                            if bid_volume + ask_volume > 0:
                                imbalance = abs(bid_volume - ask_volume) / (bid_volume + ask_volume)

                                # High imbalance indicates poor timing
                                if imbalance > 0.7:
                                    return False

            # Check spread conditions
            for exchange, books in order_books.items():
                for symbol, book in books.items():
                    if isinstance(book, dict):
                        bids = book.get('bids', [])
                        asks = book.get('asks', [])

                        if bids and asks:
                            spread = (float(asks[0][0]) - float(bids[0][0])) / float(asks[0][0])

                            # Wide spreads indicate poor timing
                            if spread > 0.005:  # 0.5% spread threshold
                                return False

            return True

        except Exception as e:
            logger.debug(f"Error checking microstructure timing: {e}")
            return True

    def _check_volatility_volume_timing(self, market_context: MarketContext, current_time: datetime) -> bool:
        """Enhanced volatility and volume timing analysis"""
        try:
            technical_data = market_context.technical_indicators.get('BTC-USD', {})

            # Get volatility with time decay consideration
            volatility = technical_data.get('volatility', 0.02)
            volume_ratio = technical_data.get('volume_ratio', 1.0)

            # Time-based volatility requirements
            hour = current_time.hour

            # OPTIMIZED: Reduced volatility requirements for more trading opportunities
            min_volatility = 0.010  # Reduced from 0.015 to 0.010
            if hour < 8 or hour > 20:  # Off-peak hours
                min_volatility = 0.018  # Reduced from 0.025 to 0.018
            elif 12 <= hour <= 14:  # Lunch hours
                min_volatility = 0.015  # Reduced from 0.02 to 0.015

            if volatility < min_volatility:
                return False

            # OPTIMIZED: Reduced volume requirements for more trading opportunities
            min_volume_ratio = 0.5  # Reduced from 0.7 to 0.5
            if hour < 8 or hour > 20:  # Off-peak hours
                min_volume_ratio = 0.7  # Reduced from 0.9 to 0.7

            if volume_ratio < min_volume_ratio:
                return False

            # Check for volatility spikes (may indicate poor timing)
            if volatility > 0.08:  # Very high volatility
                return False

            return True

        except Exception as e:
            logger.debug(f"Error checking volatility/volume timing: {e}")
            return True

    async def _check_news_event_timing(self, market_context: MarketContext, current_time: datetime) -> bool:
        """Check for news events that might affect timing"""
        try:
            news_data = market_context.news_data

            # Check for recent high-impact news (within last 30 minutes)
            recent_threshold = current_time - timedelta(minutes=30)

            for news_item in news_data:
                if isinstance(news_item, dict):
                    news_time_str = news_item.get('timestamp')
                    if news_time_str:
                        try:
                            news_time = datetime.fromisoformat(news_time_str.replace('Z', '+00:00'))
                            if news_time > recent_threshold:
                                # Check if it's high-impact news
                                title = news_item.get('title', '').lower()
                                content = news_item.get('content', '').lower()

                                high_impact_keywords = [
                                    'fed', 'federal reserve', 'interest rate', 'inflation',
                                    'regulation', 'ban', 'etf approval', 'institutional'
                                ]

                                if any(keyword in title or keyword in content for keyword in high_impact_keywords):
                                    logger.debug(f"[TIME] Recent high-impact news detected: {title[:50]}...")
                                    return False
                        except:
                            continue

            return True

        except Exception as e:
            logger.debug(f"Error checking news timing: {e}")
            return True

    def _check_momentum_timing(self, market_context: MarketContext) -> bool:
        """Check momentum conditions for optimal timing"""
        try:
            technical_data = market_context.technical_indicators.get('BTC-USD', {})

            # Check multiple timeframe momentum alignment
            momentum_1h = technical_data.get('momentum_1h', 0)
            momentum_4h = technical_data.get('momentum_4h', 0)
            momentum_1d = technical_data.get('momentum_1d', 0)

            # Require some momentum alignment for good timing
            momentum_values = [momentum_1h, momentum_4h, momentum_1d]
            momentum_values = [m for m in momentum_values if m != 0]  # Filter out zeros

            if len(momentum_values) >= 2:
                # Check if momentum is aligned (same direction)
                positive_momentum = sum(1 for m in momentum_values if m > 0)
                negative_momentum = sum(1 for m in momentum_values if m < 0)

                # Good timing requires momentum alignment
                alignment_ratio = max(positive_momentum, negative_momentum) / len(momentum_values)
                if alignment_ratio < 0.6:  # Less than 60% alignment
                    return False

            # Check for momentum strength
            avg_momentum = sum(abs(m) for m in momentum_values) / len(momentum_values) if momentum_values else 0
            if avg_momentum < 0.01:  # Very weak momentum
                return False

            return True

        except Exception as e:
            logger.debug(f"Error checking momentum timing: {e}")
            return True

    async def _apply_time_based_profit_optimization(self, signal: SignalData, market_context: MarketContext) -> Optional[SignalData]:
        """
        TIME FACTOR 3: Apply time-based filtering to optimize for profitability
        """
        try:
            # Only allow signals with high confidence for profitable trading
            if signal.confidence < 0.7:
                logger.debug(f"[TIME] Signal confidence {signal.confidence:.2f} too low for profitable trading")
                return None

            # Check if market conditions support the signal direction
            technical_data = market_context.technical_indicators.get('BTC-USD', {})
            momentum = technical_data.get('momentum_1h', 0)

            if signal.action == 'BUY' and momentum < -0.1:
                logger.debug("[TIME] BUY signal rejected - negative hourly momentum")
                return None
            elif signal.action == 'SELL' and momentum > 0.1:
                logger.debug("[TIME] SELL signal rejected - positive hourly momentum")
                return None

            # Enhance signal with time-based profit optimization
            signal.confidence *= 1.1  # Boost confidence for time-optimized signals
            signal.confidence = min(signal.confidence, 1.0)

            return signal

        except Exception as e:
            logger.error(f"Error applying time-based profit optimization: {e}")
            return signal

    def _calculate_optimal_time_horizon(self, signal: SignalData, market_context: MarketContext) -> str:
        """
        Calculate optimal time horizon for the trade based on market conditions
        """
        try:
            technical_data = market_context.technical_indicators.get('BTC-USD', {})
            volatility = technical_data.get('volatility', 0.02)

            # Higher volatility = shorter time horizon for quick profits
            if volatility > 0.05:
                return "short_term_5_15min"
            elif volatility > 0.03:
                return "medium_term_30_60min"
            else:
                return "long_term_2_4hours"

        except Exception as e:
            logger.error(f"Error calculating time horizon: {e}")
            return "medium_term_30_60min"

    def _calculate_profit_target_time(self, signal: SignalData) -> str:
        """
        Calculate expected time to reach profit target
        """
        try:
            # Base profit target time on signal confidence
            if signal.confidence > 0.8:
                return "15_30_minutes"
            elif signal.confidence > 0.7:
                return "30_60_minutes"
            else:
                return "1_2_hours"

        except Exception as e:
            logger.error(f"Error calculating profit target time: {e}")
            return "30_60_minutes"
    
    async def _switch_strategy(self, new_strategy: str, performance_scores: Dict[str, float]):
        """Switch to new strategy"""
        try:
            old_strategy = self.active_strategy
            
            # Check if switch is beneficial enough
            old_score = performance_scores.get(old_strategy, 0.5)
            new_score = performance_scores.get(new_strategy, 0.5)
            
            if new_score > old_score + self.strategy_switch_threshold:
                self.active_strategy = new_strategy
                
                # Record strategy switch
                self.strategy_switches.append({
                    'from_strategy': old_strategy,
                    'to_strategy': new_strategy,
                    'old_score': old_score,
                    'new_score': new_score,
                    'timestamp': datetime.now(),
                    'reason': 'performance_based'
                })
                
                logger.info(f"Strategy switched: {old_strategy} -> {new_strategy} "
                           f"(score improvement: {new_score - old_score:.3f})")
            
        except Exception as e:
            logger.error(f"Error switching strategy: {e}")
    
    def _should_evaluate_strategies(self) -> bool:
        """Check if strategies should be evaluated"""
        return datetime.now() - self.last_evaluation > self.strategy_evaluation_interval
    
    def _get_strategy_confidence(self, strategy_name: str) -> float:
        """Get confidence score for a strategy"""
        try:
            performance = self.strategy_performance.get(strategy_name)
            if not performance or performance.total_trades < 5:
                return 0.5
            
            # Base confidence on recent performance
            recent_performance = performance.recent_performance[-10:]
            if not recent_performance:
                return 0.5
            
            wins = sum(1 for p in recent_performance if p > 0)
            confidence = wins / len(recent_performance)
            
            return confidence
            
        except Exception:
            return 0.5
    
    async def record_trade_result(self, strategy_name: str, pnl: float):
        """Record trade result for strategy performance tracking"""
        try:
            # Remove "_managed" suffix if present
            base_strategy = strategy_name.replace('_managed', '')
            
            if base_strategy in self.strategy_performance:
                performance = self.strategy_performance[base_strategy]
                performance.total_trades += 1
                performance.total_pnl += pnl
                performance.recent_performance.append(pnl)
                
                # Keep only recent performance
                if len(performance.recent_performance) > 100:
                    performance.recent_performance = performance.recent_performance[-100:]
                
                if pnl > 0:
                    performance.winning_trades += 1
                
                # Update drawdown
                if len(performance.recent_performance) > 1:
                    cumulative = np.cumsum(performance.recent_performance)
                    running_max = np.maximum.accumulate(cumulative)
                    drawdown = (running_max - cumulative).max()
                    performance.max_drawdown = max(performance.max_drawdown, drawdown)
                
                performance.last_updated = datetime.now()
                
                # Train neural selector if available
                if self.neural_enabled and self.neural_selector:
                    await self._train_neural_selector(base_strategy, pnl)
                
        except Exception as e:
            logger.error(f"Error recording trade result: {e}")
    
    async def _train_neural_selector(self, strategy_name: str, pnl: float):
        """Train neural selector based on trade results"""
        try:
            # This would implement the training logic for the neural selector
            # For now, it's a placeholder
            pass
        except Exception as e:
            logger.error(f"Error training neural selector: {e}")
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """Get comprehensive strategy statistics"""
        try:
            stats = {
                'active_strategy': self.active_strategy,
                'total_switches': len(self.strategy_switches),
                'neural_enabled': self.neural_enabled,
                'strategies': {}
            }
            
            for strategy_name, performance in self.strategy_performance.items():
                stats['strategies'][strategy_name] = {
                    'total_trades': performance.total_trades,
                    'win_rate': performance.win_rate,
                    'total_pnl': performance.total_pnl,
                    'profit_factor': performance.profit_factor,
                    'max_drawdown': performance.max_drawdown,
                    'confidence': self._get_strategy_confidence(strategy_name)
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting strategy statistics: {e}")
            return {'error': str(e)}

# Global instance
_neural_strategy_manager = None

def get_neural_strategy_manager(config: Optional[Dict[str, Any]] = None) -> NeuralStrategyManager:
    """Get or create global neural strategy manager"""
    global _neural_strategy_manager
    if _neural_strategy_manager is None:
        _neural_strategy_manager = NeuralStrategyManager(config or {})
    return _neural_strategy_manager
