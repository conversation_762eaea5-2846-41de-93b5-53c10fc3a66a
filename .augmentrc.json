{"version": "2.0", "projectName": "AutoGPT Trader", "projectType": "ai-cryptocurrency-trading-platform", "systemContext": "Live cryptocurrency trading system operating on REAL MONEY with ZERO TOLERANCE FOR FAILURE. Enterprise-level automated trading platform leveraging cutting-edge neural networks, machine learning, and AI-driven decision making. GOLDEN RULE: MAXIMUM PROFIT IN MINIMUM TIME enforcement. CRITICAL EFFICIENCY DIRECTIVE: Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately. BACKTESTING COMPLIANCE: ONLY backtesting is authorized for simulated profit generation using real market data for training purposes exclusively.", "configuration": {"instructionsFile": ".github/copilot-instructions.md", "augmentFile": ".augment", "systemPrompt": "This is a LIVE CRYPTOCUR<PERSON>NCY TRADING SY<PERSON><PERSON> operating on REAL MONEY with ZERO TOLERANCE FOR FAILURE. Follow all comprehensive instructions in .github/copilot-instructions.md. Enforce GOLDEN RULE: MAXIMUM PROFIT IN MINIMUM TIME. CRITICAL EFFICIENCY DIRECTIVE: Do not be lazy and solve problems immediately. ALL imports must be correct and verified. ALL names must be defined with no undefined references. ALL objects must be defined with no attribute errors. NO asyncio errors are tolerated. NO syntax errors are acceptable. Code must be efficient, advanced, functional, effective, clean, complete and foolproof.", "constraints": ["X:\\ drive operation MANDATORY - E:\\ drive FORBIDDEN", "Real market data only - NO simulation", "100% success rate required", "Enterprise production standards", "Maximum neural network coverage", "GOLDEN RULE profit maximization enforcement", "CRITICAL EFFICIENCY DIRECTIVE: Do not be lazy and solve problems immediately", "ALL imports must be correct and verified", "ALL names must be defined with no undefined references", "ALL objects must be defined with no attribute errors", "NO asyncio errors are tolerated", "NO syntax errors are acceptable", "Code must be efficient, advanced, functional, effective, clean, complete and foolproof", "BACKTESTING ONLY for simulated profit generation - real data training purposes exclusively", "Live trading uses REAL money only - backtesting uses REAL data for simulation", "100% functional backtesting system required for neural network training", "EMOJI USAGE IS STRICTLY PROHIBITED - No emojis allowed in ANY code, documentation, logs, comments, configuration, or user-facing output", "ALL AI agents, developers, and documentation must avoid emojis in ALL contexts"]}, "goldenRule": {"principle": "MAXIMUM PROFIT IN MINIMUM TIME", "profitTargets": {"tradeMinimum": "2%", "dailyTarget": "20%", "weeklyTarget": "75%", "monthlyTarget": "300%"}, "capitalAllocation": "90% of available balance per trade", "aiConfidenceThreshold": "65% minimum"}, "neuralSystems": {"metaController": "src/neural/meta_strategy_controller.py", "hftAgent": "src/neural/hft_agent.py", "lstmProcessor": "src/neural/lstm_trading_processor.py", "quantumModules": "src/quantum_trading/", "reinforcementLearning": "src/neural/rl_agents/", "totalSystems": "15+"}, "exchangeAPIs": {"coinbase": {"implementation": "src/exchanges/coinbase.py", "authType": "CDP API with JWT tokens", "features": ["spot", "advanced_trading", "portfolio"]}, "bybit": {"implementation": "src/exchanges/bybit.py", "authType": "Unified Trading Account", "features": ["spot", "futures", "margin", "options"]}}, "security": {"encryptionKeyId": "66c4c378-f65b-4a7d-a23f-37d8936dc66e", "encryptionSystem": "HybridCrypto with post-quantum support", "vaultIntegration": "HashiCorp Vault", "hsmIntegration": "AWS CloudHSM", "postQuantumCrypto": ["Kyber768", "Classic McEliece", "SPHINCS+"]}, "performanceTargets": {"orderLatency": "<100ms", "dataProcessing": "<10ms", "neuralInference": "<50ms", "riskChecks": "<5ms", "systemUptime": "99.9%"}, "documentationMaintenance": {"updateRequirements": ["Update .github/copilot-instructions.md for new features", "Update .augment configuration file", "Update .augmentrc.json with feature descriptions"], "mandatoryProtocol": "All new features MUST be documented"}}